# sleeve_matching.py

import pandas as pd
import math
import re
import logging  # 新增导入

# 导入日志配置
from logger_config import get_sleeve_matching_logger, log_function_start, log_function_end, log_function_error, log_process_step, log_data_info, log_match_result

# 获取套管匹配日志记录器
logger = get_sleeve_matching_logger()
logger.setLevel(logging.WARNING)  # 只显示WARNING及以上日志


def decimal_places(x):
    if pd.isnull(x):
        return 0
    s = str(x)
    if '.' in s:
        return len(s.split('.')[-1].rstrip('0'))
    return 0


def match_sleeve(wire_count_df, parallel_df, sleeve_spec_path, project_type, multi_core_df=None):
    """
    套管匹配函数
    输入:
        wire_count_df: 导线统计结果DataFrame
        parallel_df: 并线统计结果DataFrame
        multi_core_df: 多芯线统计结果DataFrame
        sleeve_spec_path: 套管匹配表文件路径
        project_type: 项目工程类型（如"通用"、"福建"）
    输出:
        包含套管数据的DataFrame, 残值量表list
    """
    try:
        logger.info(f"开始套管匹配，项目类型: {project_type}")
        logger.info(f"套管匹配表路径: {sleeve_spec_path}")

        # 记录输入数据状态
        wire_count_msg = f"导线统计数据: {len(wire_count_df)}行" if wire_count_df is not None and not wire_count_df.empty else "无导线统计数据"
        parallel_msg = f"并线统计数据: {len(parallel_df)}行" if parallel_df is not None and not parallel_df.empty else "无并线统计数据"
        logger.info(f"输入数据状态 - {wire_count_msg}, {parallel_msg}")

        # 读取套管匹配表
        logger.info(f"正在读取套管匹配表: {sleeve_spec_path}")
        sleeve_spec = pd.read_excel(sleeve_spec_path, sheet_name='Sheet1')
        logger.info(f"成功读取套管匹配表，总规则数: {len(sleeve_spec)}")
        # === 新增：记录损耗率和最小有效值 ===
        # 允许缺省为0
        sleeve_loss_rate_map = {}
        sleeve_min_valid_map = {}
        sleeve_min_valid_decimal_map = {}
        for idx, row in sleeve_spec.iterrows():
            code = row.get('套管星瀚编码')
            loss_rate = row.get('损耗率', 0) if '损耗率' in row else 0
            min_valid = row.get('最小有效值', 0) if '最小有效值' in row else 0
            sleeve_loss_rate_map[code] = float(loss_rate) if pd.notna(loss_rate) else 0
            sleeve_min_valid_map[code] = float(min_valid) if pd.notna(min_valid) else 0
            sleeve_min_valid_decimal_map[code] = decimal_places(min_valid)

        # 1. 根据项目工程类型过滤套管匹配表
        if project_type == "福建":
            # 福建项目：只使用福建专用套管
            project_sleeve = sleeve_spec[sleeve_spec['工程要求'] == project_type]
        else:
            # 非福建项目：排除福建专用套管（黄色套管），避免使用福建专用套管
            project_sleeve = sleeve_spec[sleeve_spec['工程要求'] != '福建']
        logger.info(f"过滤后符合工程类型 '{project_type}' 的规则数: {len(project_sleeve)}")

        if len(project_sleeve) == 0:
            logger.warning(f"警告: 没有找到符合工程类型 '{project_type}' 的套管匹配规则")

        # 准备结果存储
        results = []
        wire_match_count = 0
        parallel_double_count = 0
        parallel_single_count = 0

        # 2. 处理导线统计数据
        if wire_count_df is not None and not wire_count_df.empty:
            logger.info(f"开始处理 {len(wire_count_df)} 条导线统计数据")
            # 拆分设备类型
            split_devices = wire_count_df['设备类型（起点/终点）'].str.split('/', expand=True)
            wire_count_df['起点设备类型'] = split_devices[0]
            wire_count_df['终点设备类型'] = split_devices[1]

            for idx, row in wire_count_df.iterrows():
                cabinet = row['屏柜编号']
                diameter = row['对应线径']
                count = row['导线根数']
                wire_type = row['颜色/线径标识']  # 获取线型标识

                # 确定乘数因子（两芯线*2，四芯线*4，其他*1）
                multiplier = 1
                if wire_type == "两芯线":
                    multiplier = 2
                    logger.debug(f"  导线类型: 两芯线 (数量乘数: 2)")
                elif wire_type == "四芯线":
                    multiplier = 4
                    logger.debug(f"  导线类型: 四芯线 (数量乘数: 4)")

                # 确定接口要求
                start_interface = "接地铜排" if "接地铜排" in row['起点设备类型'] else "非接地铜排"
                end_interface = "接地铜排" if "接地铜排" in row['终点设备类型'] else "非接地铜排"
                logger.debug(f"导线 {idx + 1}/{len(wire_count_df)}: 屏柜 {cabinet}, 线径 {diameter}, "
                             f"起点接口: {start_interface}, 终点接口: {end_interface}")

                # 4. 套接根数均为"单"
                sleeve_type = "单"

                # 5. 匹配起点端套管（套接根数为"单"）
                logger.debug(f"  匹配起点端套管 (套接根数: 单)")
                start_match = project_sleeve[
                    (project_sleeve['对应线径'] == diameter) &
                    (project_sleeve['接口要求'] == start_interface) &
                    (project_sleeve['套接根数'] == "单")
                    ]

                if not start_match.empty:
                    sleeve = start_match.iloc[0]
                    code = sleeve['套管星瀚编码']
                    results.append({
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': code,
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': count * multiplier,  # 应用乘数因子
                        '来源': '导线统计(起点-单)',
                        '损耗率': sleeve_loss_rate_map.get(code, 0),
                        '最小有效值': sleeve_min_valid_map.get(code, 0),
                        '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                    })
                    wire_match_count += 1
                    logger.debug(f"    匹配成功: {sleeve['套管名称']} (数量: {count * multiplier})")
                else:
                    logger.warning(f"  警告: 线径 {diameter} 起点接口 {start_interface} 未找到单套管匹配")

                # 匹配终点端套管（套接根数为"单"）
                logger.debug(f"  匹配终点端套管 (套接根数: 单)")
                end_match = project_sleeve[
                    (project_sleeve['对应线径'] == diameter) &
                    (project_sleeve['接口要求'] == end_interface) &
                    (project_sleeve['套接根数'] == "单")
                    ]

                if not end_match.empty:
                    sleeve = end_match.iloc[0]
                    code = sleeve['套管星瀚编码']
                    results.append({
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': code,
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': count * multiplier,  # 应用乘数因子
                        '来源': '导线统计(终点-单)',
                        '损耗率': sleeve_loss_rate_map.get(code, 0),
                        '最小有效值': sleeve_min_valid_map.get(code, 0),
                        '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                    })
                    wire_match_count += 1
                    logger.debug(f"    匹配成功: {sleeve['套管名称']} (数量: {count * multiplier})")
                else:
                    logger.warning(f"  警告: 线径 {diameter} 终点接口 {end_interface} 未找到单套管匹配")

                # 新增：为两芯线和四芯线匹配套接根数为"合"的套管
                if wire_type in ["两芯线", "四芯线"]:
                    logger.debug(f"  特殊线型 {wire_type} 开始匹配套接根数为'合'的套管")

                    # 匹配起点端套管（套接根数为"合"）
                    logger.debug(f"    匹配起点端套管 (套接根数: 合)")
                    start_he_match = project_sleeve[
                        (project_sleeve['对应线径'] == diameter) &
                        (project_sleeve['接口要求'] == start_interface) &
                        (project_sleeve['套接根数'] == "合")
                        ]

                    if not start_he_match.empty:
                        sleeve = start_he_match.iloc[0]
                        code = sleeve['套管星瀚编码']
                        results.append({
                            '屏柜编号': cabinet,
                            '套管名称': sleeve['套管名称'],
                            '套管型号': sleeve['套管型号'],
                            '套管星瀚编码': code,
                            '套管星空编码': sleeve['套管星空编码'],
                            '单端长度': sleeve['单端长度/米'],
                            '系统单位长度': sleeve['系统单位长度/米'],
                            '色带型号': sleeve['色带型号'],
                            '数量': count,  # 不应用乘数因子
                            '来源': '导线统计(起点-合)',
                            '损耗率': sleeve_loss_rate_map.get(code, 0),
                            '最小有效值': sleeve_min_valid_map.get(code, 0),
                            '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                        })
                        wire_match_count += 1
                        logger.debug(f"      匹配成功: {sleeve['套管名称']} (数量: {count})")
                    else:
                        logger.warning(f"    警告: 线径 {diameter} 起点接口 {start_interface} 未找到合套管匹配")

                    # 匹配终点端套管（套接根数为"合"）
                    logger.debug(f"    匹配终点端套管 (套接根数: 合)")
                    end_he_match = project_sleeve[
                        (project_sleeve['对应线径'] == diameter) &
                        (project_sleeve['接口要求'] == end_interface) &
                        (project_sleeve['套接根数'] == "合")
                        ]

                    if not end_he_match.empty:
                        sleeve = end_he_match.iloc[0]
                        code = sleeve['套管星瀚编码']
                        results.append({
                            '屏柜编号': cabinet,
                            '套管名称': sleeve['套管名称'],
                            '套管型号': sleeve['套管型号'],
                            '套管星瀚编码': code,
                            '套管星空编码': sleeve['套管星空编码'],
                            '单端长度': sleeve['单端长度/米'],
                            '系统单位长度': sleeve['系统单位长度/米'],
                            '色带型号': sleeve['色带型号'],
                            '数量': count,  # 不应用乘数因子
                            '来源': '导线统计(终点-合)',
                            '损耗率': sleeve_loss_rate_map.get(code, 0),
                            '最小有效值': sleeve_min_valid_map.get(code, 0),
                            '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                        })
                        wire_match_count += 1
                        logger.debug(f"      匹配成功: {sleeve['套管名称']} (数量: {count})")
                    else:
                        logger.warning(f"    警告: 线径 {diameter} 终点接口 {end_interface} 未找到合套管匹配")
                else:
                    logger.debug(f"  非特殊线型 ({wire_type})，跳过合套管匹配")

            logger.info(f"导线统计处理完成，成功匹配 {wire_match_count} 条套管需求")

            # 3. 处理并线统计数据（从导线统计的if块中移出来，独立处理）
            if parallel_df is not None and not parallel_df.empty:
                logger.info(f"开始处理 {len(parallel_df)} 条并线统计数据")

                # === 新增：预处理并线分组 ===
                # 提取组号前缀（并线组号中"_"右侧":"左侧的部分）
                parallel_df['组号前缀'] = parallel_df['并线组号'].apply(
                    lambda x: x.split('_', 1)[1].split(':', 1)[0] if '_' in x and ':' in x else None
                )

                # 按屏柜和组号前缀分组
                grouped = parallel_df.groupby(['屏柜号', '组号前缀'])
                group_results = []  # 存储分组处理结果

                for (cabinet, group_key), group_df in grouped:
                    logger.debug(f"处理组: 屏柜 {cabinet}, 组号前缀 {group_key}")

                    # 分别统计颜色/线径标识1和颜色/线径标识2中的棕($)和蓝($)数量
                    # 标识1列的统计
                    brown_count_1 = 0
                    blue_count_1 = 0
                    # 标识2列的统计
                    brown_count_2 = 0
                    blue_count_2 = 0

                    for _, row in group_df.iterrows():
                        # 统计颜色/线径标识1列
                        if str(row['颜色/线径标识1']) == '棕($)':
                            brown_count_1 += 1
                        if str(row['颜色/线径标识1']) == '蓝($)':
                            blue_count_1 += 1
                        # 统计颜色/线径标识2列
                        if str(row['颜色/线径标识2']) == '棕($)':
                            brown_count_2 += 1
                        if str(row['颜色/线径标识2']) == '蓝($)':
                            blue_count_2 += 1

                    # 取组内第一条记录的线径（假设组内线径一致）
                    sample_row = group_df.iloc[0]
                    diameter1 = sample_row['对应线径1']
                    diameter2 = sample_row['对应线径2']

                    # 匹配颜色/线径标识1列的双并套管
                    if brown_count_1 > 0 and blue_count_1 > 0:
                        logger.debug(f"  检测到标识1列棕蓝双并线（棕:{brown_count_1}条, 蓝:{blue_count_1}条), 线径 {diameter1}")

                        double_match_1 = project_sleeve[
                            (project_sleeve['对应线径'] == diameter1) &
                            (project_sleeve['套接根数'] == "双")
                            ]

                        if not double_match_1.empty:
                            sleeve = double_match_1.iloc[0]
                            code = sleeve['套管星瀚编码']
                            results.append({
                                '屏柜编号': cabinet,
                                '套管名称': sleeve['套管名称'],
                                '套管型号': sleeve['套管型号'],
                                '套管星瀚编码': code,
                                '套管星空编码': sleeve['套管星空编码'],
                                '单端长度': sleeve['单端长度/米'],
                                '系统单位长度': sleeve['系统单位长度/米'],
                                '色带型号': sleeve['色带型号'],
                                '数量': min(brown_count_1, blue_count_1),  # 按最小数量配对
                                '来源': '并线统计(组双并-标识1)',
                                '损耗率': sleeve_loss_rate_map.get(code, 0),
                                '最小有效值': sleeve_min_valid_map.get(code, 0),
                                '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                            })
                            parallel_double_count += min(brown_count_1, blue_count_1)
                            logger.debug(f"  标识1列组双并套管匹配成功: {sleeve['套管名称']} (数量: {min(brown_count_1, blue_count_1)})")
                        else:
                            logger.warning(f"  警告: 标识1列线径 {diameter1} 未找到双并套管匹配")

                    # 匹配颜色/线径标识2列的双并套管
                    if brown_count_2 > 0 and blue_count_2 > 0:
                        logger.debug(f"  检测到标识2列棕蓝双并线（棕:{brown_count_2}条, 蓝:{blue_count_2}条), 线径 {diameter2}")

                        double_match_2 = project_sleeve[
                            (project_sleeve['对应线径'] == diameter2) &
                            (project_sleeve['套接根数'] == "双")
                            ]

                        if not double_match_2.empty:
                            sleeve = double_match_2.iloc[0]
                            code = sleeve['套管星瀚编码']
                            results.append({
                                '屏柜编号': cabinet,
                                '套管名称': sleeve['套管名称'],
                                '套管型号': sleeve['套管型号'],
                                '套管星瀚编码': code,
                                '套管星空编码': sleeve['套管星空编码'],
                                '单端长度': sleeve['单端长度/米'],
                                '系统单位长度': sleeve['系统单位长度/米'],
                                '色带型号': sleeve['色带型号'],
                                '数量': min(brown_count_2, blue_count_2),  # 按最小数量配对
                                '来源': '并线统计(组双并-标识2)',
                                '损耗率': sleeve_loss_rate_map.get(code, 0),
                                '最小有效值': sleeve_min_valid_map.get(code, 0),
                                '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                            })
                            parallel_double_count += min(brown_count_2, blue_count_2)
                            logger.debug(f"  标识2列组双并套管匹配成功: {sleeve['套管名称']} (数量: {min(brown_count_2, blue_count_2)})")
                        else:
                            logger.warning(f"  警告: 标识2列线径 {diameter2} 未找到双并套管匹配")

                    # 将组内记录添加到待处理列表
                    for _, row in group_df.iterrows():
                        group_results.append(row)

                # 处理所有记录（包括未分组的和分组后的）
                processed_records = group_results if group_results else parallel_df.to_dict('records')
                logger.info(f"分组处理后记录数: {len(processed_records)}")

                # 处理每条并线记录
                for record in processed_records:
                    cabinet = record['屏柜号']
                    diameter1 = record['对应线径1']
                    diameter2 = record['对应线径2']
                    color1 = record['颜色/线径标识1']
                    color2 = record['颜色/线径标识2']
                    devices1 = record['设备类型1'].split('/', 1) if pd.notna(record['设备类型1']) else ["", ""]
                    devices2 = record['设备类型2'].split('/', 1) if pd.notna(record['设备类型2']) else ["", ""]

                    logger.debug(f"并线: 屏柜 {cabinet}, 线径1: {diameter1}({color1}), 线径2: {diameter2}({color2})")

                    # 单根套管匹配（每根导线单独处理）
                    logger.debug("  按单根线处理")
                    for i, (diameter, color, devices) in enumerate(
                            [(diameter1, color1, devices1),
                             (diameter2, color2, devices2)], 1):

                        # 确定接口要求
                        interface = "接地铜排" if "接地铜排" in devices[0] or "接地铜排" in devices[1] else "非接地铜排"
                        logger.debug(f"  处理导线{i}: 线径 {diameter}, 接口 {interface}")

                        # 匹配套管
                        single_match = project_sleeve[
                            (project_sleeve['对应线径'] == diameter) &
                            (project_sleeve['接口要求'] == interface) &
                            (project_sleeve['套接根数'] == "单")
                            ]

                        if not single_match.empty:
                            sleeve = single_match.iloc[0]
                            code = sleeve['套管星瀚编码']
                            results.append({
                                '屏柜编号': cabinet,
                                '套管名称': sleeve['套管名称'],
                                '套管型号': sleeve['套管型号'],
                                '套管星瀚编码': code,
                                '套管星空编码': sleeve['套管星空编码'],
                                '单端长度': sleeve['单端长度/米'],
                                '系统单位长度': sleeve['系统单位长度/米'],
                                '色带型号': sleeve['色带型号'],
                                '数量': 1,  # 并线统计中每根导线数量为1
                                '来源': f'并线统计(导线{i})',
                                '损耗率': sleeve_loss_rate_map.get(code, 0),
                                '最小有效值': sleeve_min_valid_map.get(code, 0),
                                '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                            })
                            parallel_single_count += 1
                            logger.debug(f"  导线{i}套管匹配成功: {sleeve['套管名称']}")
                        else:
                            logger.warning(f"  警告: 线径 {diameter} 接口 {interface} 未找到匹配套管")

                logger.info(f"并线统计处理完成: 双并套管 {parallel_double_count} 条, 单根套管 {parallel_single_count} 条")

        # 6.5. 处理多芯线统计表
        multi_core_match_count = 0
        if multi_core_df is not None and not multi_core_df.empty:
            logger.info(f"开始处理多芯线统计表: {len(multi_core_df)} 条记录")

            for idx, row in multi_core_df.iterrows():
                cabinet = row['屏柜编号']
                device_type = row['设备类型']
                wire_type = row['颜色/线径标识']  # '四芯线' 或 '两芯线'
                diameter = row['对应线径']  # 0.12 或 0.3

                logger.debug(f"多芯线 {idx + 1}/{len(multi_core_df)}: 屏柜 {cabinet}, 类型 {wire_type}, 线径 {diameter}")

                # 确定乘数因子（两芯线*2，四芯线*4）
                multiplier = 1
                if wire_type == "两芯线":
                    multiplier = 2
                    logger.debug(f"  导线类型: 两芯线 (数量乘数: 2)")
                elif wire_type == "四芯线":
                    multiplier = 4
                    logger.debug(f"  导线类型: 四芯线 (数量乘数: 4)")
                else:
                    logger.debug(f"  导线类型: {wire_type} (数量乘数: 1)")

                # 确定接口要求（从设备类型判断）
                interface = "接地铜排" if "接地铜排" in device_type else "非接地铜排"
                logger.debug(f"  接口要求: {interface}")

                # 匹配套接根数为"单"的套管（起点和终点各一个）
                single_match = project_sleeve[
                    (project_sleeve['对应线径'] == diameter) &
                    (project_sleeve['接口要求'] == interface) &
                    (project_sleeve['套接根数'] == "单")
                ]

                if not single_match.empty:
                    sleeve = single_match.iloc[0]
                    code = sleeve['套管星瀚编码']

                    # 起点端套管
                    results.append({
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': code,
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': 1 * multiplier,  # 应用乘数因子
                        '来源': f'多芯线统计(起点-单)',
                        '损耗率': sleeve_loss_rate_map.get(code, 0),
                        '最小有效值': sleeve_min_valid_map.get(code, 0),
                        '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                    })

                    # 终点端套管
                    results.append({
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': code,
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': 1 * multiplier,  # 应用乘数因子
                        '来源': f'多芯线统计(终点-单)',
                        '损耗率': sleeve_loss_rate_map.get(code, 0),
                        '最小有效值': sleeve_min_valid_map.get(code, 0),
                        '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                    })

                    multi_core_match_count += 2
                    logger.debug(f"    匹配成功: {sleeve['套管名称']} (起点+终点, 各数量: {1 * multiplier})")
                else:
                    logger.warning(f"  警告: 多芯线线径 {diameter} 接口 {interface} 未找到单套管匹配")

                # 为多芯线匹配套接根数为"合"的套管
                he_match = project_sleeve[
                    (project_sleeve['对应线径'] == diameter) &
                    (project_sleeve['接口要求'] == interface) &
                    (project_sleeve['套接根数'] == "合")
                ]

                if not he_match.empty:
                    sleeve = he_match.iloc[0]
                    code = sleeve['套管星瀚编码']

                    # 起点端套管（套接根数为"合"）
                    results.append({
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': code,
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': 1,  # 不应用乘数因子
                        '来源': f'多芯线统计(起点-合)',
                        '损耗率': sleeve_loss_rate_map.get(code, 0),
                        '最小有效值': sleeve_min_valid_map.get(code, 0),
                        '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                    })

                    # 终点端套管（套接根数为"合"）
                    results.append({
                        '屏柜编号': cabinet,
                        '套管名称': sleeve['套管名称'],
                        '套管型号': sleeve['套管型号'],
                        '套管星瀚编码': code,
                        '套管星空编码': sleeve['套管星空编码'],
                        '单端长度': sleeve['单端长度/米'],
                        '系统单位长度': sleeve['系统单位长度/米'],
                        '色带型号': sleeve['色带型号'],
                        '数量': 1,  # 不应用乘数因子
                        '来源': f'多芯线统计(终点-合)',
                        '损耗率': sleeve_loss_rate_map.get(code, 0),
                        '最小有效值': sleeve_min_valid_map.get(code, 0),
                        '最小有效值小数位数': sleeve_min_valid_decimal_map.get(code, 0)
                    })

                    multi_core_match_count += 2
                    logger.debug(f"    匹配成功: {sleeve['套管名称']} (起点+终点-合, 各数量: 1)")
                else:
                    logger.debug(f"  多芯线线径 {diameter} 接口 {interface} 未找到合套管匹配")

            logger.info(f"多芯线统计处理完成: {multi_core_match_count} 条套管记录")
        else:
            logger.info("无多芯线统计数据，跳过多芯线套管处理")

        # 7. 汇总结果
        if not results:
            logger.warning("没有匹配到任何套管需求，返回空数据框")
            return pd.DataFrame(), []

        logger.info(f"共匹配到 {len(results)} 条套管需求记录")
        result_df = pd.DataFrame(results)

        # 8. 计算套管总长度
        result_df['套管总长度'] = result_df['单端长度'] * result_df['数量']
        logger.debug(f"已计算套管总长度")

        # 9. 按屏柜编号、套管星瀚编码等分组汇总
        logger.info("开始按屏柜编号、套管星瀚编码等分组汇总套管需求")
        group_cols = [
            '屏柜编号', '套管名称', '套管型号', '套管星瀚编码', '套管星空编码', '色带型号', '来源', '系统单位长度',
            '损耗率', '最小有效值', '最小有效值小数位数'
        ]
        final_df = result_df.groupby(group_cols, dropna=False).agg({
            '套管总长度': 'sum',
            '数量': 'sum'
        }).reset_index()
        final_df = final_df.rename(columns={'数量': '合并数量', '来源': '数据来源'})

        # 10. 计算系统分子和残值
        system_molecule_list = []
        residual_list = []
        for idx, row in final_df.iterrows():
            total_length = row['套管总长度']
            unit_length = row['系统单位长度']
            loss_rate = row['损耗率']
            min_valid = row['最小有效值']
            min_dec = int(row['最小有效值小数位数']) if not pd.isnull(row['最小有效值小数位数']) else 2
            code = row['套管星瀚编码']
            sleeve_name = row['套管名称']
            cabinet = row['屏柜编号']
            # 使用decimal模块进行精确的套管计算
            from decimal_calculator import calculate_sleeve_residual
            truncated_value, residual = calculate_sleeve_residual(
                total_length, unit_length, loss_rate, min_dec
            )
            system_molecule_list.append(truncated_value)

            # 记录残值（使用更精确的阈值判断）
            # print(f"原始分子计算使用decimal模块, 截断后的分子值: {truncated_value}, 最小有效值的小数位数: {min_dec}")
            if abs(residual) > 1e-15:  # 使用更严格的阈值
                residual_list.append({
                    '物料编码': code,
                    '物料名称': sleeve_name,
                    '残值': residual
                })
            # print(f"物料编码: {code}, 物料名称: {sleeve_name}, 残值: {residual}")
        final_df['系统分子'] = system_molecule_list
        logger.info(f"分组汇总后得到 {len(final_df)} 条唯一套管记录")

        return final_df[
            ['屏柜编号', '套管名称', '套管型号', '套管星瀚编码', '套管星空编码', '套管总长度', '系统单位长度', '色带型号', '合并数量', '数据来源', '系统分子', '损耗率', '最小有效值', '最小有效值小数位数']
        ], residual_list

    except Exception as e:
        logger.exception("套管匹配过程中发生错误")
        raise